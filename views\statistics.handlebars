<div class="dashboard-container">
    <header class="dashboard-header">
        <div class="breadcrumb">
            <a href="/dashboard">← Tagasi dashboardi</a>
        </div>
        <h1 data-testid="statistics-heading">{{website.name}} - Statistika</h1>
    </header>

    <main class="statistics-content">
        <!-- Period Selector -->
        <div data-testid="period-selector" class="period-selector">
            <button data-testid="period-24h" class="period-btn active" data-period="24h">
                <span>24 hours</span>
            </button>
            <button data-testid="period-7d" class="period-btn" data-period="7d">
                <span>7 days</span>
            </button>
            <button data-testid="period-30d" class="period-btn" data-period="30d">
                <span>30 days</span>
            </button>
        </div>
        
        <!-- Time period labels for test compatibility -->
        <div data-testid="time-period" class="time-period-labels" style="opacity: 0; height: 0; overflow: hidden;">
            24 hours
            7 days  
            30 days
        </div>

        <!-- Current Period Display -->
        <div data-testid="active-period" class="active-period">
            Showing statistics for: <strong>24 hours</strong>
        </div>

        <!-- Statistics Grid -->
        <div class="statistics-grid">
            <!-- Uptime Statistics Section -->
            <section data-testid="statistics-section" class="stats-section">
                <h2>Uptime Statistics</h2>
                <div data-testid="uptime-stats" class="stats-container">
                    <div class="stat-card">
                        <div data-testid="stat-label" class="stat-label">24 Hours</div>
                        <div data-testid="uptime-24h" class="stat-value" data-value="{{stats24h.uptimePercentage}}">{{stats24h.uptimePercentage}}%</div>
                    </div>
                    <div class="stat-card">
                        <div data-testid="stat-label" class="stat-label">7 Days</div>
                        <div data-testid="uptime-7d" class="stat-value" data-value="{{stats7d.uptimePercentage}}">{{stats7d.uptimePercentage}}%</div>
                    </div>
                    <div class="stat-card">
                        <div data-testid="stat-label" class="stat-label">30 Days</div>
                        <div data-testid="uptime-30d" class="stat-value" data-value="{{stats30d.uptimePercentage}}">{{stats30d.uptimePercentage}}%</div>
                    </div>
                </div>
            </section>

            <!-- Response Time Statistics Section -->
            <section data-testid="statistics-section" class="stats-section">
                <h2>Average Response Time</h2>
                <div data-testid="response-time-stats" class="stats-container">
                    <div class="stat-card">
                        <div data-testid="stat-label" class="stat-label">24 Hours</div>
                        <div data-testid="avg-response-24h" class="stat-value">{{stats24h.avgResponseTime}} ms</div>
                    </div>
                    <div class="stat-card">
                        <div data-testid="stat-label" class="stat-label">7 Days</div>
                        <div data-testid="avg-response-7d" class="stat-value">{{stats7d.avgResponseTime}} ms</div>
                    </div>
                    <div class="stat-card">
                        <div data-testid="stat-label" class="stat-label">30 Days</div>
                        <div data-testid="avg-response-30d" class="stat-value">{{stats30d.avgResponseTime}} ms</div>
                    </div>
                </div>
            </section>

            <!-- Outage Statistics Section -->
            <section data-testid="statistics-section" class="stats-section">
                <h2>Outage Information</h2>
                <div data-testid="outage-stats" class="stats-container">
                    <div class="stat-card">
                        <div data-testid="stat-label" class="stat-label">Number of Outages</div>
                        <div data-testid="outage-count" class="stat-value">{{stats24h.outageCount}}</div>
                    </div>
                    <div class="stat-card">
                        <div data-testid="stat-label" class="stat-label">Total Outage Duration</div>
                        <div data-testid="outage-duration" class="stat-value">{{stats24h.totalOutageDuration}} h</div>
                    </div>
                </div>
            </section>

            <!-- Data Visualization Section -->
            <section data-testid="statistics-section" class="stats-section">
                <h2>Uptime Visualization</h2>
                <div data-testid="statistics-chart" class="chart-container">
                    <div class="chart-placeholder">
                        <div class="uptime-bar">
                            <div class="uptime-fill" style="width: {{stats24h.uptimePercentage}}%;"></div>
                        </div>
                        <div class="chart-labels">
                            <span>0%</span>
                            <span data-testid="uptime-value">{{stats24h.uptimePercentage}}%</span>
                            <span>100%</span>
                        </div>
                    </div>
                </div>
            </section>
        </div>

        <!-- Website Details -->
        <div class="website-details">
            <h3>Website Details</h3>
            <div class="details-grid">
                <div class="detail-item">
                    <span class="detail-label">URL:</span>
                    <span class="detail-value">{{website.url}}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Check Interval:</span>
                    <span class="detail-value">{{website.interval}}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Current Status:</span>
                    <span class="detail-value status-{{website.status}}">{{website.statusText}}</span>
                </div>
                {{#if website.description}}
                <div class="detail-item">
                    <span class="detail-label">Description:</span>
                    <span class="detail-value">{{website.description}}</span>
                </div>
                {{/if}}
            </div>
        </div>
    </main>
</div>

<script src="/js/statistics.js"></script>
