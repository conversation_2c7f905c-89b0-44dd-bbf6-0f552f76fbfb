<div class="dashboard-container">
    <header class="dashboard-header">
        <h1>Teavituste seaded</h1>
        <div class="dashboard-controls">
            <a href="/dashboard" data-testid="back-to-dashboard" class="btn btn-secondary">Tagasi dashboardile</a>
        </div>
    </header>

    <main class="dashboard-content">
        <div class="settings-container">
            {{#if showSuccess}}
            <div data-testid="settings-success-message" class="success-message" style="display: block;">
                <p>Notification settings saved</p>
            </div>
            {{else}}
            <div data-testid="settings-success-message" class="success-message" style="display: none;">
                <p>Notification settings saved</p>
            </div>
            {{/if}}

            <form id="notification-settings-form" method="POST" action="/settings/notifications">
                <div class="form-group">
                    <label for="notification-email">Email address for notifications:</label>
                    <input type="email" id="notification-email" name="email" data-testid="notification-email-input"
                        value="{{notificationSettings.email}}" placeholder="<EMAIL>"
                        class="form-control">
                </div>

                <div class="form-group">
                    <label for="consecutive-fails">Consecutive failures before notification:</label>
                    <input type="number" id="consecutive-fails" name="consecutiveFailures"
                        data-testid="consecutive-fails-input" value="{{notificationSettings.consecutiveFailures}}"
                        min="1" max="10" class="form-control">
                </div>

                <div class="form-group" data-testid="notification-sites-selection">
                    <label>Select sites for notifications:</label>
                    <div class="sites-selection">
                        {{#each websites}}
                        <div class="site-checkbox-item">
                            <input type="checkbox" id="site-{{this.id}}" name="enabledSites" value="{{this.id}}"
                                data-testid="site-notification-checkbox" {{#if (contains
                                ../notificationSettings.enabledSites this.id)}}checked{{/if}}>
                            <label for="site-{{this.id}}">
                                <strong>{{this.name}}</strong> - {{this.url}}
                            </label>
                        </div>
                        {{/each}}
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" data-testid="save-notification-settings" class="btn btn-primary">
                        Save Settings
                    </button>
                </div>
            </form>
        </div>
    </main>
</div>

<style>
    .settings-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .form-control:focus {
        outline: none;
        border-color: #0066cc;
        box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
    }

    .sites-selection {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        background: #f9f9f9;
    }

    .site-checkbox-item {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }

    .site-checkbox-item:last-child {
        margin-bottom: 0;
    }

    .site-checkbox-item input[type="checkbox"] {
        margin-right: 10px;
        width: auto;
    }

    .site-checkbox-item label {
        margin-bottom: 0;
        cursor: pointer;
        font-weight: normal;
    }

    .form-actions {
        text-align: center;
        margin-top: 30px;
    }

    .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        font-size: 14px;
        font-weight: 600;
    }

    .btn-primary {
        background-color: #0066cc;
        color: white;
    }

    .btn-primary:hover {
        background-color: #0052a3;
    }

    .btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background-color: #545b62;
    }

    .success-message {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 20px;
    }
</style>