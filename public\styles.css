/* Main styles for Uptime Tracker */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f9fafb;
    color: #1f2937;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

header {
    background-color: #fff;
    border-bottom: 1px solid #e5e7eb;
    padding: 20px 0;
    margin-bottom: 30px;
}

header h1 {
    text-align: center;
    margin: 0;
    color: #1f2937;
}

main {
    background-color: #fff;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

footer {
    text-align: center;
    padding: 20px;
    color: #6b7280;
    font-size: 0.875rem;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #3b82f6;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s;
}

.btn:hover {
    background-color: #2563eb;
}

.btn-primary {
    background-color: #3b82f6;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.success-actions {
    margin-top: 20px;
    display: flex;
    gap: 15px;
    justify-content: center;
}

.success-actions .btn {
    padding: 12px 24px;
}